{% extends 'base.html' %}

{% block content %}
  <div class="container mt-4">
    <!-- 主要功能区域：三列并列 -->
    <div class="row mb-4">
      <!-- 失败ID显示 -->
      <div class="col-md-3 mb-3">
        <label for="offline-ids" class="form-label">
          <i class="text-warning">●</i> 失败ID
        </label>
        <textarea
          id="offline-ids"
          class="form-control form-control"
          rows="10"
          readonly
          placeholder="失败的ID将显示在这里..."
        >{{ fail_ids|join:"&#10;" }}</textarea>
        <div class="alert alert-warning mt-2">
          <strong>失败数量:</strong> <span id="offline-count">{{ fail_ids|length|default:0 }}</span>
        </div>
      </div>
      <!-- 离线ID显示 -->
      <div class="col-md-3 mb-3">
        <label for="offline-ids" class="form-label">
          <i class="text-danger">●</i> 离线ID
        </label>
        <textarea
          id="offline-ids"
          class="form-control form-control"
          rows="10"
          readonly
          placeholder="离线的ID将显示在这里..."
        >{{ offline_ids|join:"&#10;" }}</textarea>
        <div class="alert alert-danger mt-2">
          <strong>离线数量:</strong> <span id="offline-count">{{ offline_ids|length|default:0 }}</span>
        </div>
      </div>

      <!-- 在线ID显示 -->
      <div class="col-md-3 mb-3">
        <label for="online-ids" class="form-label">
          <i class="text-success">●</i> 在线ID
        </label>
        <textarea
          id="online-ids"
          class="form-control form-control"
          rows="10"
          readonly
          placeholder="在线的ID将显示在这里..."
        >{{ online_ids|join:"&#10;" }}</textarea>
        <div class="alert alert-success mt-2">
          <strong>在线数量:</strong> <span id="online-count">{{ online_ids|length|default:0 }}</span>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="col-md-3 mb-3">
        <form method="post" id="checker-form">
          {% csrf_token %}
          <div class="mb-3">
            <label for="{{ form.ids_input.id_for_label }}" class="form-label">
              <i class="text-primary">●</i> 输入ID
            </label>
            {{ form.ids_input }}
          </div>

          <div class="d-grid gap-2">
            <button type="button" class="btn btn-secondary" id="clear-btn">
              清空
            </button>
            <button type="submit" class="btn btn-primary" id="check-btn">
              <span class="btn-text">检查状态</span>
              <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 说明 -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="alert alert-info">
          <h6><i class="fas fa-info-circle"></i> 使用说明:</h6>
          <ul class="mb-0">
            <li>在左侧输入框中输入ID，每行一个ID</li>
            <li>点击"检查状态"按钮或输入时自动检查</li>
            <li>在线ID会显示在绿色区域</li>
            <li>离线ID会显示在红色区域</li>
            <li>失败ID会显示在黄色区域</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block inline_javascript %}
  <script>
    // 等待 DOM 加载完成
    document.addEventListener('DOMContentLoaded', function () {
      // 获取表单和按钮元素
      const checkerForm = document.getElementById('checker-form');
      const checkBtn = document.getElementById('check-btn');
      const clearBtn = document.getElementById('clear-btn');

      // 如果在 checker 页面
      if (checkerForm && checkBtn) {
        // 监听表单提交事件
        checkerForm.addEventListener('submit', function (e) {
          // 显示加载状态
          showLoadingState();
        });

        // 清空按钮功能
        if (clearBtn) {
          clearBtn.addEventListener('click', function () {
            const idsInput = document.querySelector('#id_ids_input');
            if (idsInput) {
              idsInput.value = '';
            }
          });
        }
      }

      // 显示加载状态的函数
      function showLoadingState() {
        const btnText = checkBtn.querySelector('.btn-text');
        const spinner = checkBtn.querySelector('.spinner-border');

        if (btnText && spinner) {
          btnText.textContent = '检查中...';
          spinner.classList.remove('d-none');
          checkBtn.disabled = true;
        }
      }
    });
  </script>
{% endblock %}
