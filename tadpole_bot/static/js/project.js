/* Project specific Javascript goes here. */

// 等待 DOM 加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 获取表单和按钮元素
    const checkerForm = document.getElementById('checker-form');
    const checkBtn = document.getElementById('check-btn');
    const clearBtn = document.getElementById('clear-btn');

    // 如果在 checker 页面
    if (checkerForm && checkBtn) {
        // 监听表单提交事件
        checkerForm.addEventListener('submit', function(e) {
            // 显示加载状态
            showLoadingState();
        });

        // 清空按钮功能
        if (clearBtn) {
            clearBtn.addEventListener('click', function() {
                const idsInput = document.querySelector('#id_ids_input');
                if (idsInput) {
                    idsInput.value = '';
                }
            });
        }
    }

    // 显示加载状态的函数
    function showLoadingState() {
        const btnText = checkBtn.querySelector('.btn-text');
        const spinner = checkBtn.querySelector('.spinner-border');

        if (btnText && spinner) {
            btnText.textContent = '检查中...';
            spinner.classList.remove('d-none');
            checkBtn.disabled = true;
        }
    }

    // 隐藏加载状态的函数（页面重新加载后会自动重置）
    function hideLoadingState() {
        const btnText = checkBtn.querySelector('.btn-text');
        const spinner = checkBtn.querySelector('.spinner-border');

        if (btnText && spinner) {
            btnText.textContent = '检查状态';
            spinner.classList.add('d-none');
            checkBtn.disabled = false;
        }
    }
});
